{"cacheConfig": {"hostNames": ["redis.laa.np.decision-services"], "port": "6379", "abortOnConnect": false, "Enabled": true, "OperationTimeoutInMs": 300}, "dynamoDB": {"tableName": "laa-test-202723", "exclusionsTableName": "laa-test-202723-exclusions", "enabled": true}, "RLUnderwritingService": {"BaseUrl": "https://api.test.rocket-logic-uw-np.foc.zone", "Audience": "urn:ql-api:rocket-logic-uw-api-206872:Test"}, "RLBankingService": {"BaseUrl": "https://test-rlb-orch.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rlb_orchestrator-206156_test-206156:Test"}, "IncomeQualifierService": {"BaseUrl": "https://api.income-data-orchestrator.test.income-np.foc.zone", "Audience": "urn:ql-api:income_data_orchestrator-202140:Test"}, "CreditQualifierService": {"BaseUrl": "https://api.credit-data-orchestrator.test.credit-np.foc.zone", "Audience": "urn:ql-api:credit-data-orchestrator-206069:Test"}, "PropertyQualifierService": {"BaseUrl": "https://api.property-qualifier-orchestrator.test.property-np.foc.zone", "Audience": "urn:ql-api:pq-orchestrator-206067:Test"}, "Mods": {"BaseUrl": "https://api.mods.test.einstein-np.foc.zone/", "Audience": "urn:ql-api:mortgage_origination_data_service-206483:Test"}, "PropertyInsuranceQualifierService": {"BaseUrl": "https://insurance-orchestrator-test.processingservices-np.foc.zone", "Audience": "urn:ql-api:insurance.orchestrator-208699:Test"}, "RlApi": {"BaseUrl": "https://api.test.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rocketlogicapi-210205:Test"}, "OpenAmp": {"BaseUrl": "https://oagateway.test.openamp-np.foc.zone/rocketlogic/", "Audience": "urn:ql-api:oagateway-208933:Test", "IsArchiveSupported": false}, "ConfigurationManager": {"BaseUrl": "https://api.test.configuration-manager.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:configurationmanagerapi-211304:Test"}, "AmpJobRunner": {"BaseUrl": "https://api.rl-amp-job-rnr.test.np.rocket-logic.foc.zone", "Audience": "urn:ql-api:rl-amp-job-runner-211434:Test"}, "RocketLogicDocumentStorage": {"BaseUrl": "https://api.document-storage.test.origination-documents-np.foc.zone", "Audience": "urn:ql-api:document_storage-206592:Test"}, "Ders": {"BaseUrl": "https://api.doc-entity-rel.test.mortgageops.foc.zone", "Audience": "urn:ql-api:document_entity_relationship_service-210034:Test"}, "RLTasks": {"BaseUrl": "https://api.tasks.test.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:rocketlogictasksapi-211057:Test"}, "ParOrchestrator": {"BaseUrl": "https://test-par-orchestrator.np.rocket-logic.foc.zone/", "Audience": "urn:ql-api:par-orchestrator-214035:Test"}, "LicensingService": {"BaseUrl": "https://licensing-api.test.licensetheworld.foc.zone/", "Audience": "urn:ql-api:licensingservice-202059:Test"}, "AuthHub": {"TokenEndpoint": "https://sso.test.authrock.com/oauth/token"}, "TimeTrackingCheckService": {"IndividualRequestTimeoutInMilliseconds": 5000, "BackOffTimeoutInMilliseconds": 50, "BaseUrl": "https://beta-check-service.time-np.foc.zone/", "TokenEndpoint": "https://sso.beta.authrock.com/oauth/token", "Audience": "urn:ql-api:time-tracking-check-service-beta-214377:Beta"}}