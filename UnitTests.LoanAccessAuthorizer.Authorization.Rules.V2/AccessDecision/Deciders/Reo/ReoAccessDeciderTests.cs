using Credit.Data.Models.Internal.Liabilities;
using Credit.Data.Models.Liabilities.LiabilityTypes;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules;
using LoanAccessAuthorizer.Authorization.Rules.Configuration;
using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders.Reo;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.ApplicationAuthorizerDecisionStore;
using LoanAccessAuthorizer.Domain.LoanData.Models;
using LoanAccessAuthorizer.Domain.Models.Response;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;
using LoanAccessAuthorizer.Exclusions.Models;
using LoanAccessAuthorizer.RocketLogicApi.Models;
using LoanAccessAuthorizer.StateProvider.UnitTestUtils;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Property.Domain.Entities.Enums;
using Property.Domain.Entities.Properties;
using UnitTests.LoanAccessAuthorizer.PropertyQualifier;
using ApplicationId = LoanAccessAuthorizer.Authorization.Rules.ApplicationId;
using Exclusion = LoanAccessAuthorizer.Exclusions.Models.Exclusion;
using OwnedProperty = Property.Domain.Entities.Properties.OwnedProperty;

namespace UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.Deciders.Reo;

public class ReoAccessDeciderTests : IDisposable
{
    private readonly Fixture _fixture = new Fixture();

    private readonly MockRepository _mockRepository;

    private readonly Mock<PilotCheckFactory> _pilotCheckFactory;
    private readonly Mock<IPilotCheck> _pilotCheck;
    private readonly Mock<ILogger<ReoAccessDecider>> _logger;
    private readonly Mock<IApplicationAuthorizerDecisionStore> _decisionStore;
    private readonly Mock<IOptionsSnapshot<List<ReoTpoPostRegistrationFeatureConfiguration>>> _reoTpoPostRegistrationConfigs;
    private readonly Mock<StateContainer> _stateContainer;
    private readonly Mock<AccessDecisionContainer> _accessDecisionContainer;

    private readonly ReoAccessDecider _reoAccessDecider;

    public ReoAccessDeciderTests()
    {
        _mockRepository = new MockRepository(MockBehavior.Strict);
        _logger = _mockRepository.Create<ILogger<ReoAccessDecider>>(MockBehavior.Loose);
        _decisionStore = _mockRepository.Create<IApplicationAuthorizerDecisionStore>();
        _reoTpoPostRegistrationConfigs = _mockRepository.Create<IOptionsSnapshot<List<ReoTpoPostRegistrationFeatureConfiguration>>>();
        _pilotCheckFactory = _mockRepository.Create<PilotCheckFactory>();
        _pilotCheck = _mockRepository.Create<IPilotCheck>();
        _stateContainer = _mockRepository.CreateStateContainer();
        _accessDecisionContainer = _mockRepository.CreateAccessDecisionContainer(_stateContainer);

        _pilotCheckFactory
            .Setup(factory => factory(PilotCheckType.Leader, ApplicationId.REO))
            .Returns(_pilotCheck.Object);
        var configs = _fixture.Build<ReoTpoPostRegistrationFeatureConfiguration>()
                .With(config => config.StartDate, DateTime.Now)
                .With(config => config.TpoPartnerBankIds, new HashSet<string>() { "*" })
                .CreateMany(1);
        _reoTpoPostRegistrationConfigs.SetupGet(x => x.Value).Returns(configs.ToList());

        _reoAccessDecider = new ReoAccessDecider(
            _decisionStore.Object,
            _pilotCheckFactory.Object,
            _reoTpoPostRegistrationConfigs.Object,
            _logger.Object
        );
    }

    public void Dispose()
    {
        _mockRepository.VerifyAll();
    }

    [Fact]
    public async Task CanClearDecision_ExistingDecisionTrue_ReturnsTrue()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        var existingDecision = true;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var exclusionInformation = _fixture.Create<ExclusionInformation>();

        _stateContainer
            .Setup(container => container.Get<ExclusionInformation>())
            .ReturnsAsync(exclusionInformation);

        var result = await _reoAccessDecider.CanClearDecision(_accessDecisionContainer.Object);

        Assert.True(result);
    }

    [Fact]
    public async Task CanClearDecision_HasNoExclusions_ReturnsTrue()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var exclusionInformation = _fixture.Create<ExclusionInformation>();

        _stateContainer
            .Setup(container => container.Get<ExclusionInformation>())
            .ReturnsAsync(exclusionInformation);

        var result = await _reoAccessDecider.CanClearDecision(_accessDecisionContainer.Object);

        Assert.True(result);
    }

    [Fact]
    public async Task CanClearDecision_HasExclusions_ReturnsFalse()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var exclusionInformation = new ExclusionInformation(
            initialLoanState.LoanNumber,
            _fixture
                .Build<Exclusion>()
                .With(exclusion => exclusion.AppId, ApplicationId.REO)
                .CreateMany()
        );
        _stateContainer
            .Setup(container => container.Get<ExclusionInformation>())
            .ReturnsAsync(exclusionInformation);

        var result = await _reoAccessDecider.CanClearDecision(_accessDecisionContainer.Object);

        Assert.False(result);
    }

    [Fact]
    public async Task GetExclusionDecision_LegacyDecisionFalse_ReturnsExcluded()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        var existingDecision = false;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Theory]
    [AutoDomainInlineData(true, false)]
    [AutoDomainInlineData(false, true)]
    public async Task GetExclusionDecision_IsReloOrAssumption_ReturnsExcluded(bool isRelo, bool isAssumption)
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        var existingDecision = true;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(lead => lead.IsUnsupportedLeadType, false)
            .With(lead => lead.IsRelocation, isRelo)
            .With(lead => lead.IsAssumption, isAssumption)
            .Create();

        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(false);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, [ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed]));

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_NotAvailable_ReturnsExcluded()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(rlbLeadDetails => rlbLeadDetails.IsUnsupportedLeadType, true)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(false);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();

        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_Post21RmaLoan_ReturnsExcluded()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, true)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(leadDetails => leadDetails.IsUnsupportedLeadType, true)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var ampLoanStatus21 = _fixture
            .Build<AmpLoanStatus>()
            .With(status => status.LoanStatusId, LoanStatus.FolderReceived.ToString())
            .Create();
        _stateContainer
            .Setup(container => container.Get<AmpLoanStatusCollection>())
            .ReturnsAsync(new AmpLoanStatusCollection(new[] { ampLoanStatus21 }));

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, [ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed]));

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_NonRLBOrTpoLoan_ReturnsExcluded()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        var thirdPartyOrigination = _fixture
            .Build<ThirdPartyOrigination>()
            .With(tpo => tpo.IsRocketProTPOLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<ThirdPartyOrigination>())
            .ReturnsAsync(thirdPartyOrigination);

        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(rlbLeadDetails => rlbLeadDetails.IsUnsupportedLeadType, true)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(
                true,
                true,
                [ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed, ReoAccessDecider.ReoTpoPostRegistrationFeature]));

        _stateContainer
            .Setup(container => container.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate))
            .ReturnsAsync(DateTime.Now);

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_NonRLB_NoTpoPostRegFeature_ReturnsExcluded()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(rlbLeadDetails => rlbLeadDetails.IsUnsupportedLeadType, true)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, [ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed]));

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.True(result.IsExcluded);
    }

    [Theory]
    [AutoDomainInlineData(false, true, new string[] { "*" }, ExclusionReason.RelocationLoan, "any", "01/02/2021", "01/02/2021")]
    [AutoDomainInlineData(false, false, new string[] { "*" }, ExclusionReason.NotInPilot, "any", "01/02/2021", "01/02/2021")]
    [AutoDomainInlineData(true, true, new string[] { "1", "2" }, ExclusionReason.NotInPilot, "3", "01/02/2021", "01/02/2021")]
    [AutoDomainInlineData(true, true, new string[] { "1", "3" }, ExclusionReason.RelocationLoan, "3", "01/01/2021", "01/02/2021")]
    [AutoDomainInlineData(true, true, new string[] { "1", "3" }, ExclusionReason.RelocationLoan, "3", "01/02/2021", "01/02/2021")]
    [AutoDomainInlineData(false, true, new string[] { "1", "3" }, ExclusionReason.NotInPilot, "3", "01/03/2025", "01/02/2021")]
    [AutoDomainInlineData(false, true, new string[] { }, ExclusionReason.NotInPilot, "any", "01/02/2021", "01/02/2021")]
    [AutoDomainInlineData(true, true, new string[] { "1" }, ExclusionReason.NotInPilot, "2", "01/02/2021", "01/02/2021")]
    public async Task GetExclusionDecision_NonRLB_HasTpoPostRegFeature_ReturnsExpectedExcluded(
        bool shouldHitOpenAmpTPO,
        bool featureEnabled,
        IEnumerable<string> allowedBankIds,
        ExclusionReason expectedExclusionReason,
        string tpoPartnerBankId,
        string pilotStartDate,
        string loanApplicationDate
    )
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        if (shouldHitOpenAmpTPO)
        {
            var tpoParnerCompensation = _fixture
                .Build<AmpPartnerCompensation>()
                .With(tpo => tpo.TPOPartnerID, tpoPartnerBankId)
                .CreateMany(1);

            var thirdPartyOrigination = _fixture
                .Build<ThirdPartyOrigination>()
                .With(tpo => tpo.IsRocketProTPOLoan, true)
                .With(tpo => tpo.PartnerCompensation, tpoParnerCompensation)
                .Create();

            _stateContainer
                .Setup(container => container.Get<ThirdPartyOrigination>())
                .ReturnsAsync(thirdPartyOrigination);
        }

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, true)
            .With(details => details.CreatedByAppId, ReoAccessDecider.RocketProTpoAppId)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(rlbLeadDetails => rlbLeadDetails.IsUnsupportedLeadType, true)
            .With(rlbLeadDetails => rlbLeadDetails.IsRelocation, true)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        if (featureEnabled)
        {
            _stateContainer
            .Setup(container => container.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate))
            .ReturnsAsync(DateTime.Parse(loanApplicationDate));

            var configs = _fixture.Build<ReoTpoPostRegistrationFeatureConfiguration>()
                .With(config => config.StartDate, DateTime.Parse(pilotStartDate))
                .With(config => config.TpoPartnerBankIds, allowedBankIds.ToHashSet())
                .CreateMany(2);

            _reoTpoPostRegistrationConfigs.SetupGet(x => x.Value).Returns(configs.ToList());
        }

        var featureFlags = featureEnabled ? new List<string> { ReoAccessDecider.ReoTpoPostRegistrationFeature, ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed } :
            [ ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed ];

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, featureFlags));

        var reoAccessDecider = new ReoAccessDecider(
            _decisionStore.Object,
            _pilotCheckFactory.Object,
            _reoTpoPostRegistrationConfigs.Object,
            _logger.Object
        );

        var result = await reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        Assert.Contains(result, exclusion => exclusion.Reason == expectedExclusionReason);
    }

    public enum ReoExclusionCriteria
    {
        None,
        HasRentalIncome,
        HasLiensNotOnCredit,
        HasStatus21,
        HasInvestmentSubjectProperty,
        PurchaseLoanPurpose,
        HasMultiUnitPrimarySubjectProperty,
        HasMultiUnitProperty,
        NotInPilot
    }

    [Theory]
    [AutoDomainInlineData(ReoExclusionCriteria.None, false)]
    [AutoDomainInlineData(ReoExclusionCriteria.HasStatus21, true)]
    [AutoDomainInlineData(ReoExclusionCriteria.HasInvestmentSubjectProperty, true)]
    [AutoDomainInlineData(ReoExclusionCriteria.HasMultiUnitProperty, true)]
    [AutoDomainInlineData(ReoExclusionCriteria.HasMultiUnitPrimarySubjectProperty, true)]
    [AutoDomainInlineData(ReoExclusionCriteria.PurchaseLoanPurpose, false)]
    [AutoDomainInlineData(ReoExclusionCriteria.NotInPilot, true)]
    public async Task GetExclusionDecision_HasReoExclusionCriteria_ReturnsExcluded(
        ReoExclusionCriteria reoExclusionCriteria,
        bool expectedExclusion,
        Fixture fixture
    )
    {
        var initialLoanState = fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        bool? existingDecision = null;
        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(existingDecision);

        var rocketLogicLoanDetails =
            reoExclusionCriteria == ReoExclusionCriteria.PurchaseLoanPurpose ||
            reoExclusionCriteria == ReoExclusionCriteria.HasInvestmentSubjectProperty
                ? fixture
                    .Build<RocketLogicLoanDetails>()
                    .With(loanDetails => loanDetails.LoanPurpose, LoanPurpose.Purchase)
                    .Create()
                : fixture
                    .Build<RocketLogicLoanDetails>()
                    .With(loanDetails => loanDetails.LoanPurpose, LoanPurpose.Refinance)
                    .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        var rocketLogicBankingLeadDetails = fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(lead => lead.IsUnsupportedLeadType, false)
            .With(lead => lead.IsRelocation, false)
            .With(lead => lead.IsAssumption, false)
            .Create();
        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLoanDetails = fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var qualificationGroupSet = fixture.Create<QualificationGroupSet>();

        _stateContainer
            .Setup(container => container.Get<QualificationGroupSet>())
            .ReturnsAsync(qualificationGroupSet);

        var liabilityCollection =
            reoExclusionCriteria == ReoExclusionCriteria.HasLiensNotOnCredit
                ? fixture
                    .Build<MortgageLoan>()
                    .With(liability => liability.IsSecuredByLienAgainstProperty, true)
                    .Without(liability => liability.CreditLiabilityId)
                    .CreateMany()
                : Enumerable.Empty<Liability>();

        var loanStatuses =
            reoExclusionCriteria == ReoExclusionCriteria.HasStatus21
                ? fixture
                    .Build<AmpLoanStatus>()
                    .With(status => status.LoanStatusId, LoanStatus.FolderReceived.ToString())
                    .CreateMany()
                : Enumerable.Empty<AmpLoanStatus>();
        _stateContainer
            .Setup(container => container.Get<AmpLoanStatusCollection>())
            .ReturnsAsync(new AmpLoanStatusCollection(loanStatuses));

        var subjectProperty =
            reoExclusionCriteria == ReoExclusionCriteria.HasInvestmentSubjectProperty
                ? fixture
                    .Build<SubjectProperty>()
                    .With(
                        property => property.OccupancyType,
                        OccupancyType.INVESTMENT
                    )
                    .Create()
                : fixture
                    .Build<SubjectProperty>()
                    .With(
                        property => property.OccupancyType,
                        OccupancyType.PRIMARY
                    )
                    .Create();

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        var ownedProperties = fixture.Build<OwnedProperty>()
            .With(x => x.OwnedType, OwnedType.SingleFamily)
            .With(x => x.OccupancyType, OccupancyType.PRIMARY)
            .CreateMany(1);

        if (reoExclusionCriteria == ReoExclusionCriteria.HasMultiUnitProperty)
        {
            ownedProperties.First().OwnedType = OwnedType.FiveOrMore;
        }

        if (reoExclusionCriteria == ReoExclusionCriteria.HasMultiUnitPrimarySubjectProperty)
        {
            subjectProperty.PropertyType = PropertyType.MULTI_FAMILY;
        }
        else
        {
            _stateContainer
                .Setup(container => container.Get<IEnumerable<OwnedProperty>>())
                .ReturnsAsync(ownedProperties);
        }

        _stateContainer
            .Setup(container => container.Get<SubjectProperty>())
            .ReturnsAsync(subjectProperty);

        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(reoExclusionCriteria != ReoExclusionCriteria.NotInPilot, true, [ReoAccessDecider.ReoSubjectPropertyInvestmentAllowed]));

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);
        Assert.Equal(expectedExclusion, result.IsExcluded);
    }

    [Theory]
    [InlineData(ReoAccessDecider.ReoFhaAndVaStreamlineAllowed, true, false)]
    [InlineData(ReoAccessDecider.ReoFhaAndVaStreamlineAllowed, false, true)]
    [InlineData("", true, true)]
    [InlineData("", false, true)]
    public async Task GetExclusionDecision_StreamlineLoans_ReturnsExcluded(string featureFlag, bool rcLockoutDecision, bool expectedResult)
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(true);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(lead => lead.IsUnsupportedLeadType, false)
            .With(lead => lead.IsRelocation, false)
            .With(lead => lead.IsAssumption, false)
            .Create();

        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();
        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);
        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan)).ReturnsAsync(false);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        if (featureFlag == ReoAccessDecider.ReoFhaAndVaStreamlineAllowed)
        {
            _accessDecisionContainer
                .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.RCLockout))
                .ReturnsAsync(new AccessResult(rcLockoutDecision));
        }

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, [featureFlag]));

        var qualificationGroupSet = _fixture.Create<QualificationGroupSet>();
        qualificationGroupSet.Add("FHAStreamline");

        _stateContainer
            .Setup(container => container.Get<QualificationGroupSet>())
            .ReturnsAsync(qualificationGroupSet);

        _stateContainer
            .Setup(container => container.Get<AmpLoanStatusCollection>())
            .ReturnsAsync(new AmpLoanStatusCollection([_fixture.Create<AmpLoanStatus>()]));

        var subjectProperty = _fixture.Build<SubjectProperty>()
            .With(sp => sp.OccupancyType, OccupancyType.PRIMARY)
            .Without(sp => sp.RentalIncomeDetail)
            .Create();

        var ownedProperties = _fixture.Build<OwnedProperty>()
            .With(op => op.OwnedType, OwnedType.SingleFamily)
            .With(op => op.OccupancyType, OccupancyType.PRIMARY)
            .Without(op => op.RentalIncomeDetail)
            .CreateMany(1);

        _stateContainer
            .Setup(container => container.Get<SubjectProperty>())
            .ReturnsAsync(subjectProperty);

        _stateContainer
            .Setup(container => container.Get<IEnumerable<OwnedProperty>>())
            .ReturnsAsync(ownedProperties);

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);
        Assert.Equal(expectedResult, result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_ShouldNotCallRCLockout_WhenIsTpoLoanIsTrue()
    {
        var initialLoanState = _fixture.Create<InitialLoanState>();

        _stateContainer
            .Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(initialLoanState);

        _decisionStore
            .Setup(store => store.GetDecision(initialLoanState.LoanNumber, ApplicationId.REO))
            .ReturnsAsync(true);

        var rocketLogicLoanDetails = _fixture
            .Build<RocketLogicLoanDetails>()
            .With(details => details.IsRocketLogicApiLoan, false)
            .Create();

        _stateContainer
            .Setup(container => container.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(rocketLogicLoanDetails);

        var rocketLogicBankingLeadDetails = _fixture
            .Build<RocketLogicBankingLeadDetails>()
            .With(lead => lead.IsUnsupportedLeadType, true)
            .With(lead => lead.IsRelocation, false)
            .With(lead => lead.IsAssumption, false)
            .Create();

        _stateContainer
            .Setup(stateContainer => stateContainer.Get<RocketLogicBankingLeadDetails>())
            .ReturnsAsync(rocketLogicBankingLeadDetails);

        _stateContainer.Setup(stateContainer => stateContainer.Get<bool>(ExplicitStateProviderIds.IsLoanInAmp))
            .ReturnsAsync(true);

        _stateContainer
            .Setup(container => container.Get<bool>(ExplicitStateProviderIds.IsHelocAmeripriseLoan))
            .ReturnsAsync(false);

        var rocketLogicBankingLoanDetails = _fixture
            .Create<RocketLogicBankingLoanDetails>();
        _stateContainer
            .Setup(container => container.Get<RocketLogicBankingLoanDetails>())
            .ReturnsAsync(rocketLogicBankingLoanDetails);

        _accessDecisionContainer
            .Setup(accessContainer => accessContainer.GetAccessDecision(ApplicationId.ReoTpoPreRegistration))
            .ReturnsAsync(new AccessResult(false));

        _pilotCheck
            .Setup(check => check.IsInPilot(_accessDecisionContainer.Object))
            .ReturnsAsync(new PilotCheckResult(
                true,
                true,
                [
                    ReoAccessDecider.ReoTpoPostRegistrationFeature,
                    ReoAccessDecider.ReoFhaAndVaStreamlineAllowed
                ]));

        var qualificationGroupSet = _fixture.Create<QualificationGroupSet>();
        qualificationGroupSet.Add("FHAStreamline");

        _stateContainer
            .Setup(container => container.Get<QualificationGroupSet>())
            .ReturnsAsync(qualificationGroupSet);

        _stateContainer
            .Setup(container => container.Get<AmpLoanStatusCollection>())
            .ReturnsAsync(new AmpLoanStatusCollection([_fixture.Create<AmpLoanStatus>()]));

        var thirdPartyOrigination = _fixture
            .Build<ThirdPartyOrigination>()
            .With(tpo => tpo.IsRocketProTPOLoan, true)
            .Create();

        _stateContainer
            .Setup(container => container.Get<ThirdPartyOrigination>())
            .ReturnsAsync(thirdPartyOrigination);

        var subjectProperty = _fixture.Build<SubjectProperty>()
            .With(sp => sp.OccupancyType, OccupancyType.PRIMARY)
            .Without(sp => sp.RentalIncomeDetail)
            .Create();

        var ownedProperties = _fixture.Build<OwnedProperty>()
            .With(op => op.OwnedType, OwnedType.SingleFamily)
            .With(op => op.OccupancyType, OccupancyType.PRIMARY)
            .Without(op => op.RentalIncomeDetail)
            .CreateMany(1);

        _stateContainer
            .Setup(container => container.Get<SubjectProperty>())
            .ReturnsAsync(subjectProperty);

        _stateContainer
            .Setup(container => container.Get<IEnumerable<OwnedProperty>>())
            .ReturnsAsync(ownedProperties);
        _stateContainer
            .Setup(container => container.Get<DateTime?>(ExplicitStateProviderIds.ApplicationOrInitialContactDate))
            .ReturnsAsync(DateTime.Now);

        var result = await _reoAccessDecider.GetExclusionDecision(_accessDecisionContainer.Object);

        _accessDecisionContainer
            .Verify(accessContainer => accessContainer.GetAccessDecision(ApplicationId.RCLockout), Times.Never);
        Assert.False(result.IsExcluded);
    }
}
