using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.Models.Response;
using ApplicationId = LoanAccessAuthorizer.Authorization.Rules.ApplicationId;

namespace UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.Deciders;

public class UnifiedDataLayerAssetsAccessDeciderTests : AbstractPilotAccessDeciderTests
{
    private readonly UnifiedDataLayerAssetsAccessDecider _accessDecider;
    private readonly Mock<AccessPopulationFactory> _accessPopulationFactoryMock;
    private readonly Mock<IAccessPopulation> _emdAccessPopulationMock;
    private readonly Mock<IAccessPopulation> _accessPopulationForInvestmentAccountsMock;
    private readonly Mock<IAccessPopulation> _accessPopulationForAdditionalAssetsMock;
    private readonly Mock<IAccessPopulation> _accessPopulationForNonTraditionalAssetsMock;

    public UnifiedDataLayerAssetsAccessDeciderTests() : base(ApplicationId.UnifiedDataLayerAssets, true)
    {
        _mockPilotCheckFactory.Setup(factory => factory(PilotCheckType.Leader, ApplicationId.UnifiedDataLayerAssets))
            .Returns(_mockPilotCheck.Object);
        _accessPopulationFactoryMock = _mockRepository.Create<AccessPopulationFactory>(
            Enumerable.Empty<AccessPopulationConfiguration>(), Enumerable.Empty<IAccessPopulationCreator>());
        _emdAccessPopulationMock = _mockRepository.Create<IAccessPopulation>();
        _accessPopulationForInvestmentAccountsMock = _mockRepository.Create<IAccessPopulation>();
        _accessPopulationForAdditionalAssetsMock = _mockRepository.Create<IAccessPopulation>();
        _accessPopulationForNonTraditionalAssetsMock = _mockRepository.Create<IAccessPopulation>();

        _accessPopulationFactoryMock.Setup(a => a.GetAccessPopulation("UnifiedDataLayerAssets")).Returns(_emdAccessPopulationMock.Object);
        _accessPopulationFactoryMock.Setup(a => a.GetAccessPopulation("UdlAssetsInvestmentAccounts")).Returns(_accessPopulationForInvestmentAccountsMock.Object);
        _accessPopulationFactoryMock.Setup(a => a.GetAccessPopulation("UdlAdditionalAssets")).Returns(_accessPopulationForAdditionalAssetsMock.Object);
        _accessPopulationFactoryMock.Setup(a => a.GetAccessPopulation("UdlNonTraditionalAssets")).Returns(_accessPopulationForNonTraditionalAssetsMock.Object);

        _accessDecider = new UnifiedDataLayerAssetsAccessDecider(_mockPilotCheckFactory.Object, _accessPopulationFactoryMock.Object);
    }

    [Fact]
    public async Task GetExclusionDecision_IsInPilotAndInAccessPopulation()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(false, Enumerable.Empty<string>()))
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
    }

    [Fact]
    public async Task GetExclusionDecision_HasEarnestMoneyDepositFeatureAndRLADoesNotHaveFeature()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Contains("EarnestMoneyDeposit", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_HasEarnestMoneyDepositFeatureAndRLADoesHaveFeature()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "_401K"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, ["EarnestMoneyDeposit"]))
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.DoesNotContain("EarnestMoneyDeposit", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotInPilotAndInAccessPopulation()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(false, true));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.True(result.IsExcluded);
        Assert.True(result.ShouldPersist);
    }

    [Fact]
    public async Task GetExclusionDecision_IsInPilotAndNotInAccessPopulation()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(false, []))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.True(result.IsExcluded);
        Assert.True(result.ShouldPersist);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotInPilotAndNotInAccessPopulation()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(false, false));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.True(result.ShouldPersist);
        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_IsTpo_NotInPilot()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(true);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.True(result.ShouldPersist);
        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotRocketLogic_NotInPilot()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);

        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                IsRocketLogicApiLoan = false
            });

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.True(result.ShouldPersist);
        Assert.True(result.IsExcluded);
    }

    [Fact]
    public async Task GetExclusionDecision_IsInvestmentAccounts_GetAllFeatures()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "_401K"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Equal(2, result.Features.Count());
        Assert.Contains("EarnestMoneyDeposit", result.Features);
        Assert.Contains("_401K", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotInvestmentAccounts_GetOnlyEmd()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "_401K"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Single(result.Features);
        Assert.Contains("EarnestMoneyDeposit", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotInvestmentAccountsAndInAdditionalAssets()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "_401K", "CertificateOfDeposit"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Contains("EarnestMoneyDeposit", result.Features);
        Assert.Contains("CertificateOfDeposit", result.Features);
        Assert.DoesNotContain("_401K", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNotNonTraditionalAssets()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "CreditCardRewardPoints"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Contains("EarnestMoneyDeposit", result.Features);
        Assert.DoesNotContain("CreditCardRewardPoints", result.Features);
    }

    [Fact]
    public async Task GetExclusionDecision_IsNonTraditionalAssets()
    {
        _mockStateContainer.Setup(state => state.Get<bool>(ExplicitStateProviderIds.IsTpoLoan))
            .ReturnsAsync(false);
        _mockStateContainer.Setup(state => state.Get<RocketLogicLoanDetails>())
            .ReturnsAsync(new RocketLogicLoanDetails()
            {
                RocketLogicLoanId = "loanId123"
            });
        _mockPilotCheck.Setup(pilotCheck => pilotCheck.IsInPilot(_mockAccessContainer.Object))
            .ReturnsAsync(new PilotCheckResult(true, true, ["EarnestMoneyDeposit", "CreditCardRewardPoints"]));
        _emdAccessPopulationMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _accessPopulationForInvestmentAccountsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForAdditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(false)
            .Verifiable();
        _accessPopulationForNonTraditionalAssetsMock.Setup(a => a.IsInPopulation())
            .ReturnsAsync(true)
            .Verifiable();
        _mockAccessContainer.Setup(container => container.GetAccessDecision(ApplicationId.RocketLogicAssets))
            .ReturnsAsync(new AccessResult(true, Enumerable.Empty<string>()))
            .Verifiable();

        var result = await _accessDecider.GetExclusionDecision(_mockAccessContainer.Object);

        Assert.False(result.IsExcluded);
        Assert.True(result.ShouldPersist);
        Assert.Contains("EarnestMoneyDeposit", result.Features);
        Assert.Contains("CreditCardRewardPoints", result.Features);
    }
}
