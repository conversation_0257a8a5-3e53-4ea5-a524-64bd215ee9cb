using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using LoanAccessAuthorizer.Cache.UnitTestUtils;
using LoanAccessAuthorizer.StateProvider.UnitTestUtils;
using Microsoft.Extensions.Logging;

namespace UnitTests.LoanAccessAuthorizer.Authorization.Rules.V2.State.Providers;

public class IsLoanArchivedProviderTests
{
    private readonly IFixture _fixture = new Fixture();
    private readonly MockRepository _mockRepository = new(MockBehavior.Strict);

    private readonly Mock<IArchivedLoanDataClient> _mockArchiveLoanDataClient;
    private readonly Mock<StateContainer> _mockStateContainer;
    private readonly Mock<ILogger<IsLoanArchivedProvider>> _mockLogger;

    private readonly IsLoanArchivedProvider _provider;

    private readonly string _loanNumber;
    private readonly InitialLoanState _initialLoanState;
    private readonly Mock<CachePolicy<(bool,bool)>> _cachePolicyMock;

    public IsLoanArchivedProviderTests()
    {
        _mockArchiveLoanDataClient = _mockRepository.Create<IArchivedLoanDataClient>();
        _mockStateContainer = _mockRepository.CreateStateContainer();
        _cachePolicyMock = new Mock<CachePolicy<(bool,bool)>>();
        _mockLogger = _mockRepository.Create<ILogger<IsLoanArchivedProvider>>();

        var policyFactoryMock = _mockRepository.CreateCachePolicyFactory();

        policyFactoryMock.Setup(factory => factory.CreatePolicy<(bool,bool)>(It.IsAny<TimeSpan>()))
            .Returns(_cachePolicyMock.Object);

        _provider = new IsLoanArchivedProvider(
            policyFactoryMock.Object,
            _mockArchiveLoanDataClient.Object,
            _mockLogger.Object);

        _loanNumber = _fixture.Create<string>();
        _initialLoanState = _fixture.Build<InitialLoanState>()
            .With(initialLoanState => initialLoanState.LoanNumber, _loanNumber)
            .Create();
        _mockStateContainer.Setup(container => container.Get<InitialLoanState>())
            .ReturnsAsync(_initialLoanState);
    }

    [Theory]
    [InlineData("12345", true)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public async Task GetsArchivedApplicationNumber(string archivedApplicationNumber, bool expectedResult)
    {
        Func<Task<(bool,bool)>> getIsLoanArchived = null;
        _cachePolicyMock.Setup(client =>
                client.Execute(
                    It.IsAny<Func<Task<(bool,bool)>>>(),
                    It.IsAny<CacheKey>(),
                    It.IsAny<Func<(bool,bool), bool>>(),
                    null))
            .Callback((Func<Task<(bool,bool)>> func, CacheKey key, Func<(bool,bool), bool> shouldCache, dynamic _) => getIsLoanArchived = func)
            .Returns(() => getIsLoanArchived());


        _mockArchiveLoanDataClient.Setup(client => client.GetArchivedApplicationNumber(_loanNumber))
            .ReturnsAsync(archivedApplicationNumber);

        var result = await Util.SetUpContainerAndGetState<bool>(_mockStateContainer,
            _provider, _loanNumber);

        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task GetsArchivedApplicationNumber_IgnoresException()
    {
        _mockArchiveLoanDataClient.Setup(client => client.GetArchivedApplicationNumber(_loanNumber))
            .ThrowsAsync(_fixture.Create<Exception>());

        var result = await Util.SetUpContainerAndGetState<bool>(_mockStateContainer,
            _provider, _loanNumber);

        Assert.False(result);
    }
}
