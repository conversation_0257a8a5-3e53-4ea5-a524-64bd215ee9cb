namespace LoanAccessAuthorizer.Domain.LoanData.Models;

public class ThirdPartyOrigination
{
    public bool? IsRocketProTPOLoan { get; set; }
    public bool? IsLenderFeeBuyout { get; set; }
    public bool? IsCorrespondentLoan { get; set; }
    public decimal? LenderBuyoutFeeAmount { get; set; }
    public string LoanOriginationCompanyLicenseID { get; set; }
    public IEnumerable<AmpPartnerCompensation> PartnerCompensation { get; set; }
}
