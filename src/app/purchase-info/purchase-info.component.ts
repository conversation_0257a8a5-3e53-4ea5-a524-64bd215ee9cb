import { formatCurrency, formatDate } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { map, startWith } from 'rxjs';
import { EarnestMoneyDepositComponent } from '../assets/earnest-money-deposit/earnest-money-deposit.component';
import { RlaFormFieldSuffixComponent } from '../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../assistant/directives/rla-highlight.directive';
import { CalculatedInputComponent } from '../calculated-input/calculated-input.component';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { FormSectionComponent } from '../form-section/form-section.component';
import { MilestoneChipComponent } from '../form-section/milestone-chip/milestone-chip.component';
import { FormattedDateInputComponent } from '../question-input/formatted-date-input/formatted-date-input.component';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../services/entity-state/asset-state/asset-form.service';
import { LoanFormService } from '../services/entity-state/loan-state/loan-form.service';
import { LoanInfoFormListenerService } from '../services/entity-state/loan-state/loan-info-form-listener.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { TileSkeletonComponent } from '../tile/tile-skeleton/tile-skeleton.component';
import { TileComponent } from '../tile/tile.component';
import { openFormSection } from '../util/open-form-section';
import { PurchaseInfoSkeletonComponent } from './purchase-info-skeleton/purchase-info-skeleton.component';

@Component({
  selector: 'app-purchase-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatIconModule,
    RktSkeletonModule,
    ReactiveFormsModule,
    MatSlideToggle,
    CalculatedInputComponent,
    FormattedNumberInputComponent,
    FormattedDateInputComponent,
    PurchaseInfoSkeletonComponent,
    EarnestMoneyDepositComponent,
    TileComponent,
    TileSkeletonComponent,
    MilestoneChipComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    InputSectionDirective,
  ],
  templateUrl: './purchase-info.component.html',
  styleUrl: './purchase-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PurchaseInfoComponent {
  loanStateService = inject(LoanStateService);
  formService = inject(LoanFormService);
  assetFormService = inject(AssetFormService);
  loanInfoFormListener = inject(LoanInfoFormListenerService);
  formNavSectionService = inject(FormNavSectionService);
  // Form controls as properties
  contractClosingDateControl = this.formService.loanForm.get('contractClosingDate') as FormControl;
  downPaymentAmountControl = this.formService.loanForm.get('downPaymentAmount') as FormControl;
  purchasePriceControl = this.formService.loanForm.get('purchasePrice') as FormControl;
  sellerConcessionsControl = this.formService.loanForm.get('sellerConcessions') as FormControl;
  emdAmountControl = this.assetFormService.emdForm.get('assetValue') as FormControl;
  noEmdControl = this.assetFormService.emdForm.controls.noEmd;
  isForSaleByOwnerControl = this.formService.loanForm.controls.isForSaleByOwner;
  
  // Signal for template to check if loan editing is disabled
  isLoanEditingDisabled = toSignal(this.loanStateService.isLoanEditingDisabled$, { initialValue: false });
  formSectionComponentRef = viewChild.required(FormSectionComponent);
  purchasePrice = this.loanInfoFormListener.purchasePrice;
  today = new Date();
  readonly ProductInputMilestone = ProductMilestoneFormInput;
  readonly FormSection = FormSection;
  closingDate = toSignal(
    this.contractClosingDateControl.valueChanges.pipe(
      startWith(this.contractClosingDateControl.value),
      map((value) => value),
    ),
    { initialValue: this.contractClosingDateControl.value }
  );

  purchaseInfoSectionSummary = computed(() => {
    const purchaseInfoSummaries = [];

    const price = this.purchasePrice();
    if (price !== undefined) {
      const amount = formatCurrency(price, 'en-US', '$', 'USD');

      const purchasePrice = {
        label: 'Purchase Price',
        content: amount,
      };
      purchaseInfoSummaries.push(purchasePrice);
    }

    if (this.closingDate()) {
      const date = formatDate(this.closingDate(), 'MM/dd/yyyy', 'en-US');
      const closingDate = {
        label: 'Closing Date',
        content: date,
      };
      purchaseInfoSummaries.push(closingDate);
    }

    if (this.noPurchaseInfoCollected(purchaseInfoSummaries)) {
      return [
        {
          label: '',
          content: 'No purchase information collected',
        },
      ];
    }
    return purchaseInfoSummaries;
  });

  private noPurchaseInfoCollected(summaries: { label: string; content: string }[]): boolean {
    const lengthOfOne = summaries.length === 1;
    const content = summaries[0].content;
    return (
      summaries.length === 0 ||
      (lengthOfOne && content == null) ||
      (lengthOfOne && content === '$0.00')
    );
  }

  assetFormGroup = this.assetFormService.readonlyEmdForm;
  ownersControl = computed(() => this.assetFormGroup.controls.rocketLogicClientIds);

  // Template-driven disabled state management using computed signals
  updateFormControlsDisabledState = computed(() => {
    const isDisabled = this.isLoanEditingDisabled();
    const controls = [
      this.contractClosingDateControl,
      this.purchasePriceControl,
      this.downPaymentAmountControl,
      this.sellerConcessionsControl,
      this.emdAmountControl,
      this.noEmdControl,
      this.isForSaleByOwnerControl
    ];

    controls.forEach(control => {
      if (isDisabled && control.enabled) {
        control.disable({ emitEvent: false });
      } else if (!isDisabled && control.disabled) {
        control.enable({ emitEvent: false });
      }
    });

    return isDisabled;
  });







  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  handleSignedPurchaseAgreementStatus(checked: boolean) {
    if (!checked) {
      this.emdAmountControl.reset();
      this.contractClosingDateControl.reset();
      this.ownersControl().reset();
      this.sellerConcessionsControl.reset();
      this.isForSaleByOwnerControl.reset();
      this.noEmdControl.setValue(true);

      this.emdAmountControl.markAsDirty();
      this.contractClosingDateControl.markAsDirty();
      this.ownersControl().markAsDirty();
      this.sellerConcessionsControl.markAsDirty();
      this.isForSaleByOwnerControl.markAsDirty();
      this.noEmdControl.markAsDirty();
    }
  }
}
