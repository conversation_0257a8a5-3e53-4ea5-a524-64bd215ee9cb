<app-form-section
  [title]="'Purchase Info'"
  [formGroup]="formService.loanForm"
  [formSection]="FormSection.PurchaseInfo"
>
  @if (!loanStateService.isFetching()) {
    <!-- Template-driven disabled state management -->
    <div style="display: none;">{{ updateFormControlsDisabledState() }}</div>
    <div class="signed-pa-grid">
      <mat-slide-toggle
        #signedPaToggle
        id="signed-pa-toggle"
        class="rkt-SlideToggle"
        color="accent"
        formControlName="hasSignedPurchaseAgreement"
        labelPosition="before"
        [disabled]="isLoanEditingDisabled()"
        (change)="handleSignedPurchaseAgreementStatus($event.checked)"
      >
        <span class="rkt-SlideToggle__label" id="signed-pa-title"
          >Client has Signed Purchase Agreement
        </span>
      </mat-slide-toggle>
      @if (signedPaToggle.checked) {
        <app-formatted-date-input
          id="closing-date"
          label="Closing Date"
   appNavInput [control]="contractClosingDateControl"
          [minimumDate]="today"
          [inputSection]="ProductInputMilestone.ClosingDate"
        />

        <app-formatted-number-input
          prefix="$"
          id="purchase-price"
          label="Purchase Price"
          appNavInput
          appRlaHighlight
          [allowNegative]="false"
          [control]="purchasePriceControl"
          [inputSection]="ProductInputMilestone.PurchasePrice"
        >
          <ng-container form-field-suffix>
            <app-rla-form-field-suffix />
          </ng-container>
        </app-formatted-number-input>

        <app-calculated-input
          prefix="$"
          label="Down Payment"
          id="down-payment"
          appNavInput
          [total]="purchasePrice()"
          [allowNegative]="false"
          [control]="downPaymentAmountControl"
          [inputSection]="ProductInputMilestone.DownPayment"
        />

        <app-calculated-input
          prefix="$"
          label="Seller Concessions"
          id="seller-concessions"
          [total]="purchasePrice()"
          [allowNegative]="false"
          [control]="sellerConcessionsControl"
        />

        <mat-slide-toggle
          class="rkt-SlideToggle mb-5"
          color="accent"
          id="fsbo"
          formControlName="isForSaleByOwner"
          [disabled]="isLoanEditingDisabled()"
        >
          <span class="rkt-SlideToggle__label rkt-Spacing--ml8">For Sale By Owner</span>
        </mat-slide-toggle>
      } @else {
        <app-formatted-number-input
          appNavInput
          prefix="$"
          id="max-purchase-price"
          label="Purchase Price"
          appRlaHighlight
          [allowNegative]="false"
          [control]="purchasePriceControl"
          [inputSection]="ProductInputMilestone.PurchasePrice"
        >
          <ng-container form-field-suffix>
            <app-rla-form-field-suffix />
          </ng-container>
        </app-formatted-number-input>
        <app-calculated-input
          prefix="$"
          label="Down Payment"
          id="desired-down-payment"
          [total]="purchasePrice()"
          [allowNegative]="false"
          [controlType]="ProductInputMilestone.DownPayment"
          [control]="downPaymentAmountControl"
        />
      }
    </div>
    @if (signedPaToggle.checked) {
      <div [formGroup]="assetFormService.emdForm">
        <div id="emd-container">
          <p class="rkt-Label-14 rkt-FontWeight--500" id="emd-title">Earnest Money Deposit</p>
          <mat-slide-toggle
            id="emd-toggle"
            class="rkt-SlideToggle"
            color="accent"
            labelPosition="before"
            formControlName="noEmd"
            [disabled]="isLoanEditingDisabled()"
          >
            <span class="rkt-SlideToggle__label rkt-Spacing--ml8" id="emd-toggle-text"
              >No Earnest Money Deposit</span
            ></mat-slide-toggle
          >
        </div>
        <app-earnest-money-deposit />
      </div>
    }
  } @else {
    <app-purchase-info-skeleton />
  }
  @if (!loanStateService.isFetching()) {
    <div section-summary class="row flex-wrap">
      @for (info of purchaseInfoSectionSummary(); track info) {
        <app-tile
          [label]="info?.label"
          [content]="info.content"
          (tileClick)="openFormSection()"
        />
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap">
      <app-tile-skeleton />
    </div>
  }
  @if (!loanStateService.isFetching()) {
    <div milestone-chip>
      <app-milestone-chip
        [formSection]="FormSection.PurchaseInfo"
        (chipClick)="openFormSection()"
      />
    </div>
  }
</app-form-section>
