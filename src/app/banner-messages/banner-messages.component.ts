import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BannerMessage, MessageSeverity } from '@rocket-logic/rl-xp-bff-models';
import { BannerMessageService } from '../services/banner-message/banner-message.service';
import { BannerMessageComponent } from './banner-message/banner-message.component';

/**
 * Component that displays banner messages in a carousel.
 * Uses the BannerMessageService to get messages from the server and archived loan messages.
 */
@Component({
  selector: 'app-banner-messages',
  standalone: true,
  imports: [BannerMessageComponent, MatButtonModule, MatIconModule],
  templateUrl: './banner-messages.component.html',
  styleUrls: ['./banner-messages.component.scss'],
})
export class BannerMessagesComponent {
  private readonly bannerMessageService = inject(BannerMessageService);
  
  // Message data
  private bannerMessages: BannerMessage[] = [];
  private messageIndex = 0;
  
  // Template binding properties
  hasActiveMessage = false;
  activeMessageSubject = '';
  activeMessageContent = '';
  activeMessageSeverity: MessageSeverity = MessageSeverity.Information;
  bannerMessagesLength = 0;
  messageNumber = 1;
  isFirstMessage = true;
  isLastMessage = true;
  
  constructor() {
    // Subscribe to combined banner messages from service (includes both server and archived loan messages)
    this.bannerMessageService.bannerMessages$.subscribe(messages => {
      console.log('Banner messages received:', messages);
      this.bannerMessages = messages;
      this.bannerMessagesLength = messages.length;

      // Reset index if it's beyond array bounds
      if (this.messageIndex >= this.bannerMessages.length) {
        this.messageIndex = Math.max(0, this.bannerMessages.length - 1);
      }

      this.updateTemplateProperties();
      console.log('Banner component state:', {
        hasActiveMessage: this.hasActiveMessage,
        messageCount: this.bannerMessagesLength,
        activeMessage: this.bannerMessages[this.messageIndex]
      });
    });
  }
  
  /**
   * Update all template properties based on current state
   */
  private updateTemplateProperties(): void {
    // Update derived properties
    this.hasActiveMessage = this.bannerMessages.length > 0;
    this.messageNumber = this.messageIndex + 1;
    this.isFirstMessage = this.messageIndex === 0;
    this.isLastMessage = this.messageIndex === this.bannerMessages.length - 1 || this.bannerMessages.length === 0;
    
    // Get current message properties
    const activeMessage = this.bannerMessages[this.messageIndex];
    if (activeMessage) {
      this.activeMessageSubject = activeMessage.subject || '';
      this.activeMessageContent = activeMessage.content || '';
      this.activeMessageSeverity = activeMessage.messageSeverity || MessageSeverity.Information;
    } else {
      this.activeMessageSubject = '';
      this.activeMessageContent = '';
      this.activeMessageSeverity = MessageSeverity.Information;
    }
  }



  /**
   * Navigate to the previous message
   */
  back(): void {
    if (this.messageIndex > 0) {
      this.messageIndex--;
      this.updateTemplateProperties();
    }
  }

  /**
   * Navigate to the next message
   */
  next(): void {
    if (this.messageIndex < this.bannerMessages.length - 1) {
      this.messageIndex++;
      this.updateTemplateProperties();
    }
  }
}
