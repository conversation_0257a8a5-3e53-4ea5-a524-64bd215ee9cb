import { inject, Injectable } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { BannerMessage, MessageSeverity, MessageType } from '@rocket-logic/rl-xp-bff-models';
import { of, shareReplay } from 'rxjs';
import { NotificationService } from '../notification/notification.service';

@Injectable({
  providedIn: 'root',
})
export class BannerMessageService {
  private notificationService = inject(NotificationService);
  private logger = inject(SplunkLoggerService);

  // Create a test archived loan banner message for local testing
  private createTestArchivedLoanMessage(): BannerMessage {
    return {
      messageId: 'archived-loan-test',
      messageType: MessageType.BannerMessage,
      subject: 'Archived Loan',
      content: 'This loan is archived and cannot be edited. Any changes will not be saved.',
      messageSeverity: MessageSeverity.Warn,
      isActive: true,
      timestamp: new Date().toISOString(),
      source: 'rl-xp-ui-test',
    };
  }

  // Banner messages now come entirely from the BFF, including archived loan messages
  // For local testing, always show the archived loan message
  public readonly bannerMessages$ = of([this.createTestArchivedLoanMessage()]).pipe(
    shareReplay(1),
  );

  // Original implementation (commented out for testing)
  // public readonly bannerMessages$ = this.notificationService
  //   .getConnection$(`${environment.dataProviderUrl}/messageCenter/listen/bannerMessages`)
  //   .pipe(
  //     map((message): BannerMessage[] => {
  //       const data = JSON.parse(message.data || '[]');

  //       if (!Array.isArray(data)) {
  //         return [];
  //       }

  //       return data.filter((message: BannerMessage) => message.isActive);
  //     }),
  //     catchError((err) => {
  //       this.logger.error('Error while listening for banner messages', err);
  //       return of([]);
  //     }),
  //     startWith([this.createTestArchivedLoanMessage()]),
  //     shareReplay(1),
  //   );
}
