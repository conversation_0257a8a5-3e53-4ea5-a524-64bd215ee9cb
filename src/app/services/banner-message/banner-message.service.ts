import { inject, Injectable } from '@angular/core';
import { BannerMessage, MessageSeverity, MessageType } from '@rocket-logic/rl-xp-bff-models';
import { map, of, shareReplay } from 'rxjs';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';

@Injectable({
  providedIn: 'root',
})
export class BannerMessageService {
  private loanStateService = inject(LoanStateService, { optional: true });

  // Create archived loan banner message
  private createArchivedLoanMessage(): BannerMessage {
    return {
      messageId: 'archived-loan',
      messageType: MessageType.BannerMessage,
      subject: 'Archived Loan',
      content: 'This loan is archived and cannot be edited. Any changes will not be saved.',
      messageSeverity: MessageSeverity.Warn,
      isActive: true,
      timestamp: new Date().toISOString(),
      source: 'rl-xp-ui',
    };
  }

  // Banner messages based on loan state
  public readonly bannerMessages$ = this.loanStateService?.isLoanArchived$
    ? this.loanStateService.isLoanArchived$.pipe(
        map((isLoanArchived: boolean) => {
          const messages: BannerMessage[] = [];

          // Add archived loan message if loan is archived
          if (isLoanArchived) {
            messages.push(this.createArchivedLoanMessage());
          }

          return messages;
        }),
        shareReplay(1)
      )
    : of([]).pipe(shareReplay(1));

}
