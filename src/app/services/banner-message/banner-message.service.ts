import { inject, Injectable } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { BannerMessage } from '@rocket-logic/rl-xp-bff-models';
import { catchError, map, of, shareReplay } from 'rxjs';
import { environment } from '../../../environments/environment';
import { NotificationService } from '../notification/notification.service';

@Injectable({
  providedIn: 'root',
})
export class BannerMessageService {
  private notificationService = inject(NotificationService);
  private logger = inject(SplunkLoggerService);

  // Banner messages now come entirely from the BFF, including archived loan messages
  public readonly bannerMessages$ = this.notificationService
    .getConnection$(`${environment.dataProviderUrl}/messageCenter/listen/bannerMessages`)
    .pipe(
      map((message): BannerMessage[] => {
        const data = JSON.parse(message.data || '[]');

        if (!Array.isArray(data)) {
          return [];
        }

        return data.filter((message: BannerMessage) => message.isActive);
      }),
      catchError((err) => {
        this.logger.error('Error while listening for banner messages', err);
        return of([]);
      }),
      shareReplay(1),
    );
}
