import {
  BankruptcyDeclaration as R<PERSON><PERSON><PERSON><PERSON>ruptcy,
  DeclarationInformation as R<PERSON><PERSON>lar<PERSON>Info,
  Foreclosure as RLForeclosure,
  Forfeiture as RLForfeiture,
  Mortgage as RLMortgage,
  PreForeclosureOrShortSale as RLPreForeclosureOrShortSale,
} from '@rocket-logic/rocket-logic-api-models/client';
import { Address } from '../address';

export {
  BankruptcyInformation,
  BankruptcyStatus,
  BorrowedFund,
  BorrowedFundsInformation,
  CitizenshipResidencyType,
  ClientOccupancyType,
  ClientSellerRelationship,
  CreditApplication,
  CreditApplicationInformation,
  FamilyRelationshipType,
  ForeclosureInformation,
  ForfeitureInformation,
  Lawsuit,
  LawsuitCompletionTimeframe,
  LawsuitInformation,
  LawsuitPartyType,
  LawsuitStatus,
  MortgageInformation,
  PaymentPlan,
  PreForeclosureOrShortSaleInformation,
  PropertyInformation,
  PropertyTitleHeldByType,
  SellerRelationshipInformation,
} from '@rocket-logic/rocket-logic-api-models/client';

export { BankruptcyType } from '@rocket-logic/rocket-logic-api-models/shared/enums/bankruptcy-type';

export interface Bankruptcy extends Omit<RLBankruptcy, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface Mortgage extends Omit<RLMortgage, 'address'> {
  address: Address;
}

export interface PreForeclosureOrShortSale
  extends Omit<RLPreForeclosureOrShortSale, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface Forfeiture extends Omit<RLForfeiture, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface Foreclosure extends Omit<RLForeclosure, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface DeclarationInformation
  extends Omit<
    RLDeclarationInfo,
    | 'bankruptcyInformation'
    | 'mortgageInformation'
    | 'preForeclosureOrShortSaleInformation'
    | 'forfeitureInformation'
    | 'foreclosureInformation'
  > {
  bankruptcyInformation?: {
    bankruptcies?: Bankruptcy[];
  };
  mortgageInformation?: {
    otherMortgages?: Mortgage[];
  };
  preForeclosureOrShortSaleInformation?: {
    preForeclosureOrShortSales?: PreForeclosureOrShortSale[];
  };
  forfeitureInformation?: {
    forfeitures?: Forfeiture[];
  };
  foreclosureInformation?: {
    foreclosures?: Foreclosure[];
  };
}
