using LoanAccessAuthorizer.Authorization.Rules.Pilot;
using LoanAccessAuthorizer.Authorization.Rules.V2.State;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Common.AccessPopulations;
using LoanAccessAuthorizer.Domain.PilotExclusions.Models;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.AccessDecision.AccessDeciders;

public class UnifiedDataLayerAssetsAccessDecider : IExclusionDecider
{
    public string AccessDecisionId => ApplicationId.UnifiedDataLayerAssets;
    private const string UdlAssetsInvestmentAccounts = "UdlAssetsInvestmentAccounts";
    private const string UdlAdditionalAssets = "UdlAdditionalAssets";
    private const string UdlNonTraditionalAssets = "UdlNonTraditionalAssets";

    private static readonly string[] InvestmentAccountAssetFeatures = ["_401K", "_403B","_457Plan",
        "IndividualRetirementArrangement", "Annuity", "Keogh", "ThriftSavingsPlan", "SimplifiedEmployeePension",
        "_529CollegeSavingsPlan", "MutualFund", "LifeInsurance", "TrustAccount", "BrokerageAccount"];

    private static readonly string[] AdditionalAssetFeatures = ["BridgeLoan", "CertificateOfDeposit",
        "GiftOfCash", "GiftOfEquity", "Grant", "MoneyMarket", "PledgedAssetAccount",
        "ProceedsFromSaleOfNonRealEstateAsset", "ProceedsFromSecuredLoan", "_1031Exchange"];

    private static readonly string[] NonTraditionalAssets = ["CreditCardRewardPoints",
        "EstimatedNetProceedsFromSaleOfOwnedProperty"];

    private readonly IPilotCheck _pilotCheck;
    private readonly IAccessPopulation _emdAccessPopulation;
    private readonly IAccessPopulation _udlAssetsInvestmentAccountsAccessPopulation;
    private readonly IAccessPopulation _udlAdditionalAssetsAccessPopulation;
    private readonly IAccessPopulation _udlNonTraditionalAssetsAccessPopulation;

    public UnifiedDataLayerAssetsAccessDecider(PilotCheckFactory pilotCheckFactory,
        AccessPopulationFactory accessPopulationFactory)
    {
        _pilotCheck = pilotCheckFactory(PilotCheckType.Leader, AccessDecisionId);
        _emdAccessPopulation = accessPopulationFactory.GetAccessPopulation(AccessDecisionId);
        _udlAssetsInvestmentAccountsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlAssetsInvestmentAccounts);
        _udlAdditionalAssetsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlAdditionalAssets);
        _udlNonTraditionalAssetsAccessPopulation =
            accessPopulationFactory.GetAccessPopulation(UdlNonTraditionalAssets);
    }

    public async Task<ExclusionDecision> GetExclusionDecision(AccessDecisionContainer container)
    {
        var isTpoLoanTask = container.StateContainer.Get<bool>(ExplicitStateProviderIds.IsTpoLoan);
        var rlLoanDetailsTask = container.StateContainer.Get<RocketLogicLoanDetails>();

        var isTpoLoan = await isTpoLoanTask;
        if (isTpoLoan)
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.ThirdPartyOrigination,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        var rlLoanDetails = await rlLoanDetailsTask;
        if (string.IsNullOrEmpty(rlLoanDetails?.RocketLogicLoanId))
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        var emdAccessPopulationTask = _emdAccessPopulation.IsInPopulation();
        var udlAssetsInvestmentAccountsAccessPopulationTask = _udlAssetsInvestmentAccountsAccessPopulation.IsInPopulation();
        var udlAdditionalAssetsAccessPopulationTask = _udlAdditionalAssetsAccessPopulation.IsInPopulation();
        var udlNonTraditionalAssetsAccessPopulationTask = _udlNonTraditionalAssetsAccessPopulation.IsInPopulation();
        var pilotResult = await _pilotCheck.IsInPilot(container);
        var emdAccessPopulation = await emdAccessPopulationTask;
        var udlAssetsInvestmentAccountsAccessPopulation = await udlAssetsInvestmentAccountsAccessPopulationTask;
        var udlAdditionalAssetsAccessPopulation = await udlAdditionalAssetsAccessPopulationTask;
        var udlNonTraditionalAssetsAccessPopulation = await udlNonTraditionalAssetsAccessPopulationTask;

        if (!pilotResult.IsInPilot)
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        if (!emdAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(["EarnestMoneyDeposit"]);
        }

        if (!udlAssetsInvestmentAccountsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(InvestmentAccountAssetFeatures);
        }

        if (!udlAdditionalAssetsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(AdditionalAssetFeatures);
        }

        if (!udlNonTraditionalAssetsAccessPopulation)
        {
            pilotResult.Features = pilotResult.Features.Except(NonTraditionalAssets);
        }

        var rocketLogicAssetsAccessResult = await container.GetAccessDecision(ApplicationId.RocketLogicAssets);
        var overlapFeatures = rocketLogicAssetsAccessResult.AccessDecision
            ? pilotResult.Features.Intersect(rocketLogicAssetsAccessResult.Features)
            : [];

        if (rocketLogicAssetsAccessResult.AccessDecision == true &&
            overlapFeatures.Any())
        {
            pilotResult.Features = pilotResult.Features.Except(overlapFeatures);
        }

        if (!pilotResult.Features.Any())
        {
            return new ExclusionDecision(persist: true)
            {
                new()
                {
                    Reason = ExclusionReason.NotInPilot,
                    ExcludedAt = DateTime.Now,
                }
            };
        }

        return new ExclusionDecision(persist: true, pilotResult.Features);
    }
}
