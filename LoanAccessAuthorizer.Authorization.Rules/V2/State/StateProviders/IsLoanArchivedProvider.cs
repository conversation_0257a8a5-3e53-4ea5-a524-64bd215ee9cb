using DecisionServices.Core.Cache;
using DecisionServices.Core.StateProvider;
using DecisionServices.Core.StateProvider.StateProviders;
using LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders.Model;
using LoanAccessAuthorizer.Domain.LoanData;
using Microsoft.Extensions.Logging;

namespace LoanAccessAuthorizer.Authorization.Rules.V2.State.StateProviders;

public class IsLoanArchivedProvider : ExplicitlyIdentifiedStateProvider<bool>
{
    private readonly CachePolicy<(bool shouldCache, bool isArchived)> _cachePolicy;
    private readonly IArchivedLoanDataClient _archivedLoanDataClient;
    private readonly ILogger<IsLoanArchivedProvider> _logger;

    public override string Identifier => ExplicitStateProviderIds.IsLoanArchivedInAmp;

    public IsLoanArchivedProvider(
        CachePolicyFactory cachePolicyFactory,
        IArchivedLoanDataClient loanDataClient,
        ILogger<IsLoanArchivedProvider> logger)
    {
        _cachePolicy = cachePolicyFactory.CreatePolicy<(bool shouldCache, bool result)>(TimeSpan.FromDays(30));
        _archivedLoanDataClient = loanDataClient;
        _logger = logger;
    }

    protected override async Task<bool> GetTypedState(StateContainer stateContainer)
    {
        var initialState = await stateContainer.Get<InitialLoanState>();
        var loanNumber = initialState.LoanNumber;
        var cacheKey = new CacheKey(loanNumber, Identifier);
        
        return (await _cachePolicy.Execute(() => IsLoanArchivedInAmp(loanNumber), cacheKey, 
                resultTuple => resultTuple.shouldCache ))
            .isArchived;
    }

    private async Task<(bool shouldCache, bool isArchived)> IsLoanArchivedInAmp(string loanNumber)
    {
        try
        {
            var archivedAppNumber = await _archivedLoanDataClient.GetArchivedApplicationNumber(loanNumber);
            return (true, !string.IsNullOrEmpty(archivedAppNumber));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking if loan {LoanNumber} is archived", loanNumber);
            return (false, false);
        }
    }
}
